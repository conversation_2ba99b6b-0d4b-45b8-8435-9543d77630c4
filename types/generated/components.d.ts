import type { Schema, Attribute } from '@strapi/strapi';

export interface SharedSeo extends Schema.Component {
  collectionName: 'components_shared_seos';
  info: {
    displayName: 'seo';
    icon: 'search';
    description: '';
  };
  attributes: {
    metaTitle: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 60;
      }>;
    metaDescription: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        minLength: 50;
        maxLength: 160;
      }>;
    metaImage: Attribute.Media<'images' | 'files' | 'videos'>;
    metaSocial: Attribute.Component<'shared.meta-social', true>;
    keywords: Attribute.Text;
    metaRobots: Attribute.String;
    structuredData: Attribute.JSON;
    metaViewport: Attribute.String;
    canonicalURL: Attribute.String;
  };
}

export interface SharedPartnership extends Schema.Component {
  collectionName: 'components_shared_partnerships';
  info: {
    displayName: 'partnership';
    icon: 'typhoon';
  };
  attributes: {
    is_partnership: Attribute.Boolean & Attribute.DefaultTo<false>;
    organisation: Attribute.String;
  };
}

export interface SharedMetaSocial extends Schema.Component {
  collectionName: 'components_shared_meta_socials';
  info: {
    displayName: 'metaSocial';
    icon: 'project-diagram';
  };
  attributes: {
    socialNetwork: Attribute.Enumeration<['Facebook', 'Twitter']> &
      Attribute.Required;
    title: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 60;
      }>;
    description: Attribute.String &
      Attribute.Required &
      Attribute.SetMinMaxLength<{
        maxLength: 65;
      }>;
    image: Attribute.Media<'images' | 'files' | 'videos'>;
  };
}

export interface SharedCookieButton extends Schema.Component {
  collectionName: 'components_shared_cookie_buttons';
  info: {
    displayName: 'Cookie Button';
    icon: 'mouse-pointer';
  };
  attributes: {
    buttonType: Attribute.Enumeration<['Primary', 'Secondary', 'Text']>;
    label: Attribute.String;
  };
}

export interface SharedButton extends Schema.Component {
  collectionName: 'components_shared_buttons';
  info: {
    displayName: 'button';
    description: '';
  };
  attributes: {
    name: Attribute.String;
    url: Attribute.String;
    target: Attribute.Enumeration<['_self', '_blank']>;
  };
}

export interface SharedAuthorInfo extends Schema.Component {
  collectionName: 'components_shared_author_infos';
  info: {
    displayName: 'Author';
    description: '';
  };
  attributes: {
    name: Attribute.String;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    description: Attribute.Text;
  };
}

export interface HomepageCountdown extends Schema.Component {
  collectionName: 'components_homepage_countdowns';
  info: {
    displayName: 'Countdown';
    description: '';
  };
  attributes: {
    countdowndate: Attribute.Date;
    before_title: Attribute.String;
    backgroundImage: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    before_excerpt: Attribute.Text;
    after_title: Attribute.String;
    after_excerpt: Attribute.Text;
    bg_color: Attribute.String &
      Attribute.CustomField<'plugin::color-picker.color'>;
    icon_image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
  };
}

export interface HomepageCategoryPromo extends Schema.Component {
  collectionName: 'components_homepage_category_promos';
  info: {
    displayName: 'Category Promo';
    icon: 'layer';
    description: '';
  };
  attributes: {
    subtitle: Attribute.String;
    title: Attribute.String;
    description: Attribute.Text;
    sort: Attribute.Integer;
    button: Attribute.Component<'shared.button'>;
    image: Attribute.Media<'images'>;
    alignment: Attribute.Enumeration<['left', 'right']> &
      Attribute.DefaultTo<'left'>;
    checklist: Attribute.Component<'shared.button', true>;
  };
}

export interface CruiseLineExtraPages extends Schema.Component {
  collectionName: 'components_cruise_line_extra_page';
  info: {
    displayName: 'Extra Pages';
  };
  attributes: {
    carousel: Attribute.Media<'images', true>;
    seo: Attribute.Component<'shared.meta-social'>;
  };
}

export interface BlocksTextBlock extends Schema.Component {
  collectionName: 'components_blocks_text_blocks';
  info: {
    displayName: 'Text Block';
    icon: 'file';
    description: '';
  };
  attributes: {
    name: Attribute.Text;
  };
}

export interface BlocksTestimonial extends Schema.Component {
  collectionName: 'components_homepage_testimonials';
  info: {
    displayName: 'Testimonial';
    description: '';
  };
  attributes: {
    author: Attribute.String;
    description: Attribute.Text;
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
  };
}

export interface BlocksSplitImage extends Schema.Component {
  collectionName: 'components_blocks_split_images';
  info: {
    displayName: 'Split Image';
    icon: 'grid';
  };
  attributes: {
    first_image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    second_image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
  };
}

export interface BlocksSliders extends Schema.Component {
  collectionName: 'components_homepage_sliders';
  info: {
    displayName: 'Sliders';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.RichText;
    image: Attribute.Media<'images'>;
    permalink: Attribute.String;
    video: Attribute.JSON & Attribute.CustomField<'plugin::video-field.video'>;
    sort: Attribute.Integer & Attribute.DefaultTo<0>;
  };
}

export interface BlocksRelatedArticles extends Schema.Component {
  collectionName: 'components_blocks_related_articles';
  info: {
    displayName: 'Related Articles';
    icon: 'feather';
  };
  attributes: {
    articles: Attribute.Relation<
      'blocks.related-articles',
      'oneToMany',
      'api::insipration.insipration'
    >;
  };
}

export interface BlocksPromo extends Schema.Component {
  collectionName: 'components_blocks_promos';
  info: {
    displayName: 'Promo Block';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    description: Attribute.Text;
    sort: Attribute.Integer & Attribute.DefaultTo<0>;
    headline: Attribute.String;
  };
}

export interface BlocksInfoBox extends Schema.Component {
  collectionName: 'components_blocks_info_boxes';
  info: {
    displayName: 'Info Box';
  };
  attributes: {
    title: Attribute.String;
    sliders: Attribute.Component<'blocks.info-box-slider', true>;
  };
}

export interface BlocksInfoBoxSlider extends Schema.Component {
  collectionName: 'components_blocks_info_box_sliders';
  info: {
    displayName: 'Info Box Slider';
    icon: 'television';
    description: '';
  };
  attributes: {
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios', true>;
    url: Attribute.String;
    url_text: Attribute.String;
    tab_title: Attribute.String;
    description: Attribute.RichText &
      Attribute.CustomField<
        'plugin::ckeditor5.CKEditor',
        {
          preset: 'toolbar';
        }
      >;
    sort: Attribute.Integer & Attribute.DefaultTo<0>;
    title: Attribute.String;
  };
}

export interface BlocksImageBlock extends Schema.Component {
  collectionName: 'components_blocks_image_blocks';
  info: {
    displayName: 'Image Block';
    description: '';
  };
  attributes: {
    image: Attribute.Media<'images'>;
    url: Attribute.String;
  };
}

export interface BlocksGallery extends Schema.Component {
  collectionName: 'components_blocks_galleries';
  info: {
    displayName: 'Gallery';
    icon: 'play';
  };
  attributes: {
    images: Attribute.Media<'images' | 'files' | 'videos' | 'audios', true>;
  };
}

export interface BlocksFancyImageSection extends Schema.Component {
  collectionName: 'components_blocks_fancy_image_sections';
  info: {
    displayName: 'Fancy Image Section';
    icon: 'layer';
    description: '';
  };
  attributes: {
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    title: Attribute.String;
    description: Attribute.Text;
    text_alignment: Attribute.Enumeration<['left', 'right']> &
      Attribute.DefaultTo<'right'>;
    sort: Attribute.Integer & Attribute.DefaultTo<0>;
  };
}

export interface BlocksExploreMoreBySea extends Schema.Component {
  collectionName: 'components_shared_explore';
  info: {
    displayName: 'Explore more by sea';
    description: '';
  };
  attributes: {
    destination_guides: Attribute.Relation<
      'blocks.explore-more-by-sea',
      'oneToMany',
      'api::destination.destination'
    >;
    interests: Attribute.Relation<
      'blocks.explore-more-by-sea',
      'oneToMany',
      'api::interest.interest'
    >;
    cruise_lines: Attribute.Relation<
      'blocks.explore-more-by-sea',
      'oneToMany',
      'api::cruise-line.cruise-line'
    >;
  };
}

export interface BlocksEmbedCode extends Schema.Component {
  collectionName: 'components_blocks_embed_codes';
  info: {
    displayName: 'Embed Code';
  };
  attributes: {
    embed_code: Attribute.Text;
  };
}

export interface BlocksCallout extends Schema.Component {
  collectionName: 'components_cruise_line_callouts';
  info: {
    displayName: 'Call Out Box';
    description: '';
  };
  attributes: {
    title: Attribute.String;
    url: Attribute.String;
    saving: Attribute.String;
    description: Attribute.String;
    sub_heading: Attribute.String;
    button_text: Attribute.String;
    button_url: Attribute.String;
    terms_and_condition: Attribute.Relation<
      'blocks.callout',
      'oneToOne',
      'api::terms-and-condition.terms-and-condition'
    >;
    how_to_use: Attribute.Component<'blocks.promo', true>;
    special_discount_code: Attribute.String;
    special_discount_amount_info: Attribute.String;
    special_saving: Attribute.String;
    discount_label: Attribute.String;
    discount_button_label: Attribute.String;
  };
}

export interface BlocksCallToActions extends Schema.Component {
  collectionName: 'components_blocks_call_to_actions';
  info: {
    displayName: 'Call To Actions';
    icon: 'dashboard';
    description: '';
  };
  attributes: {
    heading: Attribute.String;
    cruise_lines: Attribute.Relation<
      'blocks.call-to-actions',
      'oneToMany',
      'api::cruise-line.cruise-line'
    >;
    description: Attribute.Text;
    sort: Attribute.Integer & Attribute.DefaultTo<0>;
    claim_texts: Attribute.Component<'blocks.text-block', true>;
    checklist: Attribute.Component<'shared.button', true>;
  };
}

export interface BlocksArticleCta extends Schema.Component {
  collectionName: 'components_blocks_article_ctas';
  info: {
    displayName: 'Article CTA';
    icon: 'collapse';
    description: '';
  };
  attributes: {
    stamp_logo: Attribute.Media<'images'>;
    stamp_text: Attribute.String;
    stamp_sub_text: Attribute.String;
    description: Attribute.Text;
    button_text: Attribute.String;
    button_link: Attribute.String;
    button_target: Attribute.Enumeration<['_self', '_blank']>;
  };
}

export interface BlocksAnnotatedImage extends Schema.Component {
  collectionName: 'components_blocks_annotated_images';
  info: {
    displayName: 'annotated_image';
  };
  attributes: {
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
  };
}

export interface BlocksAnnotatedBlocks extends Schema.Component {
  collectionName: 'components_blocks_annotated_blocks';
  info: {
    displayName: 'Annotated_blocks';
  };
  attributes: {
    image: Attribute.Media<'images' | 'files' | 'videos' | 'audios'>;
    name: Attribute.String;
  };
}

declare module '@strapi/types' {
  export module Shared {
    export interface Components {
      'shared.seo': SharedSeo;
      'shared.partnership': SharedPartnership;
      'shared.meta-social': SharedMetaSocial;
      'shared.cookie-button': SharedCookieButton;
      'shared.button': SharedButton;
      'shared.author-info': SharedAuthorInfo;
      'homepage.countdown': HomepageCountdown;
      'homepage.category-promo': HomepageCategoryPromo;
      'cruise-line.extra-pages': CruiseLineExtraPages;
      'blocks.text-block': BlocksTextBlock;
      'blocks.testimonial': BlocksTestimonial;
      'blocks.split-image': BlocksSplitImage;
      'blocks.sliders': BlocksSliders;
      'blocks.related-articles': BlocksRelatedArticles;
      'blocks.promo': BlocksPromo;
      'blocks.info-box': BlocksInfoBox;
      'blocks.info-box-slider': BlocksInfoBoxSlider;
      'blocks.image-block': BlocksImageBlock;
      'blocks.gallery': BlocksGallery;
      'blocks.fancy-image-section': BlocksFancyImageSection;
      'blocks.explore-more-by-sea': BlocksExploreMoreBySea;
      'blocks.embed-code': BlocksEmbedCode;
      'blocks.callout': BlocksCallout;
      'blocks.call-to-actions': BlocksCallToActions;
      'blocks.article-cta': BlocksArticleCta;
      'blocks.annotated-image': BlocksAnnotatedImage;
      'blocks.annotated-blocks': BlocksAnnotatedBlocks;
    }
  }
}
